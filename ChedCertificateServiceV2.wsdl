<?xml version='1.0' encoding='UTF-8'?><!-- Published by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.3.0-wls122140-b230824.1031 svn-revision#e4bad6ac24018736698e2952f77e76e7f403a9f1. --><wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2" xmlns:chedfollowup="http://ec.europa.eu/tracesnt/certificate/ched/followup/v01" xmlns:certificate="http://ec.europa.eu/tracesnt/certificate/base/v01" xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:impl="http://ec.europa.eu/tracesnt/ws/impl/certificate/ched/v2" targetNamespace="http://ec.europa.eu/tracesnt/ws/impl/certificate/ched/v2">

	<wsdl:import location="https://webgate.acceptance.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2?wsdl=1" namespace="http://ec.europa.eu/sanco/tracesnt/base/v4"/>
	
	<wsdl:types>
		<xs:schema targetNamespace="http://ec.europa.eu/tracesnt/certificate/ched/wsdl/v2">
			<xs:import schemaLocation="https://webgate.acceptance.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2?xsd=6" namespace="http://ec.europa.eu/tracesnt/certificate/ched/v2"/>
			<xs:import schemaLocation="https://webgate.acceptance.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2?xsd=3" namespace="http://ec.europa.eu/tracesnt/certificate/base/v01"/>
			<xs:import schemaLocation="https://webgate.acceptance.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2?xsd=34" namespace="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"/>
		</xs:schema>
	</wsdl:types>
	
	<wsdl:message name="SecurityMessage">
		<wsdl:part name="SecurityPart" element="wsse:Security"/>
	</wsdl:message>

	<wsdl:message name="GetChedCertificateRequestMessage">
		<wsdl:part name="GetChedCertificateRequestPart" element="ched:GetChedCertificateRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedCertificateResponseMessage">
		<wsdl:part name="GetChedCertificateResponsePart" element="ched:GetChedCertificateResponse"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedFollowUpRequestMessage">
		<wsdl:part name="GetChedFollowUpRequestPart" element="ched:GetChedFollowUpRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedFollowUpResponseMessage">
		<wsdl:part name="GetChedFollowUpResponsePart" element="ched:GetChedFollowUpResponse"/>
	</wsdl:message>

	<wsdl:message name="GetChedNonComplianceDetailsRequestMessage">
		<wsdl:part name="GetChedNonComplianceDetailsRequestPart" element="ched:GetChedNonComplianceDetailsRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedNonComplianceDetailsResponseMessage">
		<wsdl:part name="GetChedNonComplianceDetailsResponsePart" element="ched:GetChedNonComplianceDetailsResponse"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedLaboratoryTestsRequestMessage">
		<wsdl:part name="GetChedLaboratoryTestsRequestPart" element="ched:GetChedLaboratoryTestsRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedLaboratoryTestsResponseMessage">
		<wsdl:part name="GetChedLaboratoryTestsResponsePart" element="ched:GetChedLaboratoryTestsResponse"/>
	</wsdl:message>

	<wsdl:message name="GetChedPdfCertificateRequestMessage">
		<wsdl:part name="GetChedPdfCertificateRequestPart" element="ched:GetChedPdfCertificateRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedPdfCertificateResponseMessage">
		<wsdl:part name="GetChedPdfCertificateResponsePart" element="ched:GetChedPdfCertificateResponse"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedSignedPdfCertificateRequestMessage">
		<wsdl:part name="GetChedSignedPdfCertificateRequestPart" element="ched:GetChedSignedPdfCertificateRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedSignedPdfCertificateResponseMessage">
		<wsdl:part name="GetChedSignedPdfCertificateResponsePart" element="ched:GetChedSignedPdfCertificateResponse"/>
	</wsdl:message>

	<wsdl:message name="GetChedSignedXmlCertificateRequestMessage">
		<wsdl:part name="GetChedSignedXmlCertificateRequestPart" element="ched:GetChedSignedXmlCertificateRequest"/>
	</wsdl:message>

	<wsdl:message name="GetChedSignedXmlCertificateResponseMessage">
		<wsdl:part name="GetChedSignedXmlCertificateResponsePart" element="ched:GetChedSignedXmlCertificateResponse"/>
	</wsdl:message>
		
	<wsdl:message name="FindChedCertificateRequestMessage">
		<wsdl:part name="FindChedCertificateRequestPart" element="ched:FindChedCertificateRequest"/>
	</wsdl:message>
	
	<wsdl:message name="FindChedCertificateResponseMessage">
		<wsdl:part name="FindChedCertificateResponsePart" element="ched:FindChedCertificateResponse"/>
	</wsdl:message>

	<wsdl:message name="GetChedCertificateStatusRequestMessage">
		<wsdl:part name="GetChedCertificateStatusRequestPart" element="ched:GetChedCertificateStatusRequest"/>
	</wsdl:message>
	
	<wsdl:message name="GetChedCertificateStatusResponseMessage">
		<wsdl:part name="GetChedCertificateStatusResponsePart" element="ched:GetChedCertificateStatusResponse"/>
	</wsdl:message>
	
	<wsdl:message name="EmptyResponseMessage"/>
	
	<wsdl:message name="ChedCertificateNotFoundExceptionMessage">
		<wsdl:part name="fault" element="ched:ChedCertificateNotFoundException"/>
	</wsdl:message>
	
	<wsdl:message name="ChedCertificatesNotFoundExceptionMessage">
		<wsdl:part name="fault" element="ched:ChedCertificatesNotFoundException"/>
	</wsdl:message>
	
	<wsdl:message name="IllegalFindChedCertificateExceptionMessage">
		<wsdl:part name="fault" element="certificate:IllegalFindCertificateException"/>
	</wsdl:message>
	
	<wsdl:message name="ChedCertificatePermissionDeniedExceptionMessage">
		<wsdl:part name="fault" element="ched:ChedCertificatePermissionDeniedException"/>
	</wsdl:message>
	
	<wsdl:message name="SignedChedNotAvailableExceptionMessage">
		<wsdl:part name="fault" element="ched:SignedChedNotAvailableException"/>
	</wsdl:message>

	<wsdl:message name="IllegalLanguageCodeExceptionMessage">
		<wsdl:part name="fault" element="certificate:IllegalLanguageCodeException"/>
	</wsdl:message>
	
	<wsdl:portType name="ChedCertificatePort">
		<wsdl:operation name="getChedCertificate">
			<wsdl:documentation>
				Retrieve a CHED
			</wsdl:documentation>
			<wsdl:input name="GetChedCertificateRequest" message="impl:GetChedCertificateRequestMessage"/>
			<wsdl:output name="GetChedCertificateResponse" message="impl:GetChedCertificateResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getChedFollowUp">
			<wsdl:documentation>
				Retrieve a CHED FollowUp
			</wsdl:documentation>
			<wsdl:input name="GetChedFollowUpRequest" message="impl:GetChedFollowUpRequestMessage"/>
			<wsdl:output name="GetChedFollowUpResponse" message="impl:GetChedFollowUpResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getChedNonComplianceDetails">
			<wsdl:documentation>
				Retrieve a CHED Details on non-compliance
			</wsdl:documentation>
			<wsdl:input name="GetChedNonComplianceDetailsRequest" message="impl:GetChedNonComplianceDetailsRequestMessage"/>
			<wsdl:output name="GetChedNonComplianceDetailsResponse" message="impl:GetChedNonComplianceDetailsResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
		</wsdl:operation>

		<wsdl:operation name="getChedLaboratoryTests">
			<wsdl:documentation>
				Retrieve a CHED Laboratory tests
			</wsdl:documentation>
			<wsdl:input name="GetChedLaboratoryTestsRequest" message="impl:GetChedLaboratoryTestsRequestMessage"/>
			<wsdl:output name="GetChedLaboratoryTestsResponse" message="impl:GetChedLaboratoryTestsResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getChedPdfCertificate">
			<wsdl:documentation>
				Retrieve a CHED PDF
			</wsdl:documentation>
			<wsdl:input name="GetChedPdfCertificateRequest" message="impl:GetChedPdfCertificateRequestMessage"/>
			<wsdl:output name="GetChedPdfCertificateResponse" message="impl:GetChedPdfCertificateResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
			<wsdl:fault name="IllegalLanguageCodeException" message="impl:IllegalLanguageCodeExceptionMessage">
				<wsdl:documentation>
					Fault thrown when any of the extra languages specified in the request is not supported by the system.
				</wsdl:documentation>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedSignedPdfCertificate">
			<wsdl:documentation>
				Retrieve a CHED signed PDF
			</wsdl:documentation>
			<wsdl:input name="GetChedSignedPdfCertificateRequest" message="impl:GetChedSignedPdfCertificateRequestMessage"/>
			<wsdl:output name="GetChedSignedPdfCertificateResponse" message="impl:GetChedSignedPdfCertificateResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
			<wsdl:fault name="ChedSignedPdfNotAvailableException" message="impl:SignedChedNotAvailableExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getChedSignedXmlCertificate">
			<wsdl:documentation>
				Retrieve a CHED signed XML
			</wsdl:documentation>
			<wsdl:input name="GetChedSignedXmlCertificateRequest" message="impl:GetChedSignedXmlCertificateRequestMessage"/>
			<wsdl:output name="GetChedSignedXmlCertificateResponse" message="impl:GetChedSignedXmlCertificateResponseMessage"/>
			<wsdl:fault name="ChedCertificateNotFoundException" message="impl:ChedCertificateNotFoundExceptionMessage"/>
			<wsdl:fault name="ChedCertificatePermissionDeniedException" message="impl:ChedCertificatePermissionDeniedExceptionMessage"/>
			<wsdl:fault name="ChedSignedXmlNotAvailableException" message="impl:SignedChedNotAvailableExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="findChedCertificate">
			<wsdl:documentation>
				Search for a CHED
			</wsdl:documentation>
			<wsdl:input name="FindChedCertificateRequest" message="impl:FindChedCertificateRequestMessage"/>
			<wsdl:output name="FindChedCertificateResponse" message="impl:FindChedCertificateResponseMessage"/>
			<wsdl:fault name="IllegalFindChedCertificateException" message="impl:IllegalFindChedCertificateExceptionMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getChedCertificateStatus">
			<wsdl:documentation>
				Get the status of CHEDs
			</wsdl:documentation>
			<wsdl:input name="GetChedCertificateStatusRequest" message="impl:GetChedCertificateStatusRequestMessage"/>
			<wsdl:output name="GetChedCertificateStatusResponse" message="impl:GetChedCertificateStatusResponseMessage"/>
			<wsdl:fault name="ChedCertificatesNotFoundException" message="impl:ChedCertificatesNotFoundExceptionMessage"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="ChedSoapBinding" type="impl:ChedCertificatePort">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="getChedCertificate">
			<soap:operation soapAction="getChedCertificate" style="document"/>
			<wsdl:input name="GetChedCertificateRequest">
				<soap:body parts="GetChedCertificateRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedCertificateResponse">
				<soap:body parts="GetChedCertificateResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedFollowUp">
			<soap:operation soapAction="getChedFollowUp" style="document"/>
			<wsdl:input name="GetChedFollowUpRequest">
				<soap:body parts="GetChedFollowUpRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedFollowUpResponse">
				<soap:body parts="GetChedFollowUpResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedNonComplianceDetails">
			<soap:operation soapAction="getChedNonComplianceDetails" style="document"/>
			<wsdl:input name="GetChedNonComplianceDetailsRequest">
				<soap:body parts="GetChedNonComplianceDetailsRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedNonComplianceDetailsResponse">
				<soap:body parts="GetChedNonComplianceDetailsResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedLaboratoryTests">
			<soap:operation soapAction="getChedLaboratoryTests" style="document"/>
			<wsdl:input name="GetChedLaboratoryTestsRequest">
				<soap:body parts="GetChedLaboratoryTestsRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedLaboratoryTestsResponse">
				<soap:body parts="GetChedLaboratoryTestsResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedPdfCertificate">
			<soap:operation soapAction="getChedPdfCertificate" style="document"/>
			<wsdl:input name="GetChedPdfCertificateRequest">
				<soap:body parts="GetChedPdfCertificateRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedPdfCertificateResponse">
				<soap:body parts="GetChedPdfCertificateResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="IllegalLanguageCodeException">
				<soap:fault name="IllegalLanguageCodeException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedSignedPdfCertificate">
			<soap:operation soapAction="getChedSignedPdfCertificate" style="document"/>
			<wsdl:input name="GetChedSignedPdfCertificateRequest">
				<soap:body parts="GetChedSignedPdfCertificateRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedSignedPdfCertificateResponse">
				<soap:body parts="GetChedSignedPdfCertificateResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedSignedPdfNotAvailableException">
				<soap:fault name="ChedSignedPdfNotAvailableException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedSignedXmlCertificate">
			<soap:operation soapAction="getChedSignedXmlCertificate" style="document"/>
			<wsdl:input name="GetChedSignedXmlCertificateRequest">
				<soap:body parts="GetChedSignedXmlCertificateRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedSignedXmlCertificateResponse">
				<soap:body parts="GetChedSignedXmlCertificateResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificateNotFoundException">
				<soap:fault name="ChedCertificateNotFoundException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedCertificatePermissionDeniedException">
				<soap:fault name="ChedCertificatePermissionDeniedException" use="literal"/>
			</wsdl:fault>
			<wsdl:fault name="ChedSignedXmlNotAvailableException">
				<soap:fault name="ChedSignedXmlNotAvailableException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="findChedCertificate">
			<soap:operation soapAction="findChedCertificate" style="document"/>
			<wsdl:input name="FindChedCertificateRequest">
				<soap:body parts="FindChedCertificateRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="FindChedCertificateResponse">
				<soap:body parts="FindChedCertificateResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="IllegalFindChedCertificateException">
				<soap:fault name="IllegalFindChedCertificateException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
		<wsdl:operation name="getChedCertificateStatus">
			<soap:operation soapAction="getChedCertificateStatus" style="document"/>
			<wsdl:input name="GetChedCertificateStatusRequest">
				<soap:body parts="GetChedCertificateStatusRequestPart" use="literal"/>
				<soap:header message="impl:SecurityMessage" wsdl:required="true" part="SecurityPart" use="literal"/>
				<soap:header message="base:WebServiceClientIdHeader" wsdl:required="true" part="webServiceClientId" use="literal"/>
				<soap:header message="base:LanguageCodeHeader" part="languageCode" use="literal"/>
				<soap:header message="base:AttributesHeader" part="attributes" use="literal"/>
			</wsdl:input>
			<wsdl:output name="GetChedCertificateStatusResponse">
				<soap:body parts="GetChedCertificateStatusResponsePart" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="ChedCertificatesNotFoundException">
				<soap:fault name="ChedCertificatesNotFoundException" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="ChedCertificateServiceV2">
		<wsdl:port name="ChedCertificateServiceV2Endpoint" binding="impl:ChedSoapBinding">
			<soap:address location="https://webgate.acceptance.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2"/>
		</wsdl:port>
	</wsdl:service> 
	<wsp:Policy xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
		<sp:TransportBinding>
			<wsp:Policy>
				<sp:TransportToken>
					<wsp:Policy>
						<sp:HttpsToken/>
					</wsp:Policy>
				</sp:TransportToken>
				<sp:AlgorithmSuite>
					<wsp:Policy>
						<sp:Basic256/>
					</wsp:Policy>
				</sp:AlgorithmSuite>
				<sp:Layout>
					<wsp:Policy>
						<sp:Lax/>
					</wsp:Policy>
				</sp:Layout>
				<sp:IncludeTimestamp/>
			</wsp:Policy>
		</sp:TransportBinding>
		<sp:SupportingTokens>
			<wsp:Policy>
				<sp:UsernameToken sp:IncludeToken="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient">
					<wsp:Policy>
						<sp:HashPassword/>
						<sp:WssUsernameToken10/>
					</wsp:Policy>
				</sp:UsernameToken>
			</wsp:Policy>
		</sp:SupportingTokens>
	</wsp:Policy>
</wsdl:definitions>