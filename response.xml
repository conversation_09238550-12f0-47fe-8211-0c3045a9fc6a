<?xml version='1.0' encoding='UTF-8'?><S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/"><S:Header><Message xmlns="http://ec.europa.eu/sanco/tracesnt/message/v1" severity="debugging"><ID>WS_REQUEST_ID</ID><Message>dd40f902-b097-4aa2-b745-d42857569660</Message></Message></S:Header><S:Body><ns0:Fault xmlns:ns0="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://www.w3.org/2003/05/soap-envelope"><faultcode>ns0:Client</faultcode><faultstring>org.xml.sax.SAXException: cvc-complex-type.2.4.a: Invalid content was found starting with element '{"http://ec.europa.eu/tracesnt/certificate/ched/v2":PageSize}'. One of '{"http://ec.europa.eu/tracesnt/certificate/ched/v2":Type, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Status, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Purpose, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Transshipped, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Transfer, "http://ec.europa.eu/tracesnt/certificate/ched/v2":HasPlantHealthNonCompliance, "http://ec.europa.eu/tracesnt/certificate/ched/v2":HasPendingLaboratoryTests, "http://ec.europa.eu/tracesnt/certificate/ched/v2":RequiresFollowUp, "http://ec.europa.eu/tracesnt/certificate/ched/v2":IsDigitallySigned, "http://ec.europa.eu/tracesnt/certificate/ched/v2":LocalReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PhytoReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PhytoLocalReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CNCode, "http://ec.europa.eu/tracesnt/certificate/ched/v2":BCPCodeOrUnLocode, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfIssuance, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfEntry, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfDispatch, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfOrigin, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfPlaceOfDestination, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfTransferControlPoint, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfConsignor, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfConsignee, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CreateDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":UpdateDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":StatusChangeDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":DeclarationDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":DecisionDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PriorNotificationDateTimeRange}' is expected.
org.xml.sax.SAXParseException; cvc-complex-type.2.4.a: Invalid content was found starting with element '{"http://ec.europa.eu/tracesnt/certificate/ched/v2":PageSize}'. One of '{"http://ec.europa.eu/tracesnt/certificate/ched/v2":Type, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Status, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Purpose, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Transshipped, "http://ec.europa.eu/tracesnt/certificate/ched/v2":Transfer, "http://ec.europa.eu/tracesnt/certificate/ched/v2":HasPlantHealthNonCompliance, "http://ec.europa.eu/tracesnt/certificate/ched/v2":HasPendingLaboratoryTests, "http://ec.europa.eu/tracesnt/certificate/ched/v2":RequiresFollowUp, "http://ec.europa.eu/tracesnt/certificate/ched/v2":IsDigitallySigned, "http://ec.europa.eu/tracesnt/certificate/ched/v2":LocalReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PhytoReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PhytoLocalReference, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CNCode, "http://ec.europa.eu/tracesnt/certificate/ched/v2":BCPCodeOrUnLocode, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfIssuance, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfEntry, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfDispatch, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfOrigin, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfPlaceOfDestination, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfTransferControlPoint, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfConsignor, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CountryOfConsignee, "http://ec.europa.eu/tracesnt/certificate/ched/v2":CreateDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":UpdateDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":StatusChangeDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":DeclarationDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":DecisionDateTimeRange, "http://ec.europa.eu/tracesnt/certificate/ched/v2":PriorNotificationDateTimeRange}' is expected.</faultstring></ns0:Fault></S:Body></S:Envelope>