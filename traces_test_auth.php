<?php

class TracesNtChedClientTestAuth {
    private $username;
    private $authKey;
    private $clientId;
    private $useProduction;

    public function __construct($username, $authKey, $clientId, $useProduction = false) {
        $this->username = $username;
        $this->authKey = $authKey;
        $this->clientId = $clientId;
        $this->useProduction = $useProduction;
    }

    public function testAuthenticationMethods() {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2';

        // Test different digest calculation methods
        $methods = [
            1 => 'Raw nonce + created + auth_key',
            2 => 'Base64 nonce + created + auth_key', 
            3 => 'Raw nonce + created + base64_decoded_auth_key',
            4 => 'Base64 nonce + created + base64_decoded_auth_key'
        ];

        foreach ($methods as $methodNum => $description) {
            echo "\n=== Testing Method $methodNum: $description ===\n";
            
            try {
                $soapRequest = $this->createSoapRequestMethod($methodNum);
                $response = $this->sendTestRequest($endpoint, $soapRequest, $methodNum);
                
                if (strpos($response, 'UnauthenticatedException') === false) {
                    echo "✓ Method $methodNum SUCCEEDED!\n";
                    echo "Response preview: " . substr($response, 0, 200) . "...\n";
                    file_put_contents("successful_method_{$methodNum}_response.xml", $response);
                    return $methodNum; // Return successful method
                } else {
                    echo "✗ Method $methodNum failed - UnauthenticatedException\n";
                }
                
            } catch (Exception $e) {
                echo "✗ Method $methodNum failed with exception: " . $e->getMessage() . "\n";
            }
        }
        
        return false; // No method worked
    }

    private function createSoapRequestMethod($method) {
        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);
        $created = gmdate('Y-m-d\TH:i:s\Z');
        $expires = gmdate('Y-m-d\TH:i:s\Z', strtotime('+5 minutes'));
        
        // Calculate password digest based on method
        switch ($method) {
            case 1: // Raw nonce + created + auth_key
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $created . $this->authKey, true)
                );
                break;
                
            case 2: // Base64 nonce + created + auth_key
                $passwordDigest = base64_encode(
                    sha1($nonce . $created . $this->authKey, true)  
                );
                break;
                
            case 3: // Raw nonce + created + base64_decoded_auth_key
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $created . base64_decode($this->authKey), true)
                );
                break;
                
            case 4: // Base64 nonce + created + base64_decoded_auth_key
                $passwordDigest = base64_encode(
                    sha1($nonce . $created . base64_decode($this->authKey), true)
                );
                break;
                
            default:
                throw new Exception("Unknown method: $method");
        }

        echo "Nonce (base64): $nonce\n";
        echo "Created: $created\n";
        echo "Password Digest: $passwordDigest\n";

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope 
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security 
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        <ched:FindChedCertificateRequest>
            <ched:Type>P</ched:Type>
            <ched:Status>70</ched:Status>
            <ched:CountryOfIssuance>MR</ched:CountryOfIssuance>
            <ched:UpdateDateTimeRange>
                <base:From>2025-01-01T00:00:00Z</base:From>
                <base:To>2025-06-22T23:59:59Z</base:To>
            </ched:UpdateDateTimeRange>
        </ched:FindChedCertificateRequest>
    </soapenv:Body>
</soapenv:Envelope>';
    }

    private function sendTestRequest($endpoint, $soapRequest, $methodNum) {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "findChedCertificate"',
            'Content-Length: ' . strlen($soapRequest)
        ];

        file_put_contents("test_method_{$methodNum}_request.xml", $soapRequest);

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $soapRequest);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        file_put_contents("test_method_{$methodNum}_response.xml", $response);

        if ($httpCode != 200 && $httpCode != 500) {
            throw new Exception('HTTP error: ' . $httpCode);
        }

        return $response;
    }
}

// --- Test Usage ---
try {
    $client = new TracesNtChedClientTestAuth(
        'n00385tm',                          
        '0q2JpCSjeGEMUR1qQPXLg15SYiEPi9r6rntcgcXa', 
        'onispa-mr',                         
        false                           
    );

    echo "Testing different authentication methods...\n";
    echo "Auth Key Length: " . strlen('0q2JpCSjeGEMUR1qQPXLg15SYiEPi9r6rntcgcXa') . "\n";
    
    $successfulMethod = $client->testAuthenticationMethods();
    
    if ($successfulMethod) {
        echo "\n🎉 SUCCESS! Method $successfulMethod worked!\n";
        echo "Use this method in your production code.\n";
    } else {
        echo "\n❌ None of the authentication methods worked.\n";
        echo "Please check:\n";
        echo "1. Your username is correct\n";
        echo "2. Your WebService authentication key is current\n";
        echo "3. Your client ID matches what TRACES assigned\n";
        echo "4. Your WebService access is still active\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}