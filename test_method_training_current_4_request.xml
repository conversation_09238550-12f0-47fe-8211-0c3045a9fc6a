<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-686e6c8300a7e">
                <wsse:Username>n00385tm</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">vM4+AIgPk5WXWWzhBgXPeTHoIu0=</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">4nFqB8y9j9ET9AQej9wmXg==</wsse:Nonce>
                <wsu:Created>2025-07-09T13:20:03Z</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-686e6c8300a83">
                <wsu:Created>2025-07-09T13:20:03Z</wsu:Created>
                <wsu:Expires>2025-07-09T13:25:03Z</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>onispa-mr</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        <ched:FindChedCertificateRequest>
            <ched:Type>P</ched:Type>
            <ched:Status>70</ched:Status>
            <ched:CountryOfIssuance>MR</ched:CountryOfIssuance>
            <ched:UpdateDateTimeRange>
                <base:From>2024-01-01T00:00:00Z</base:From>
                <base:To>2024-06-20T23:59:59Z</base:To>
            </ched:UpdateDateTimeRange>
        </ched:FindChedCertificateRequest>
    </soapenv:Body>
</soapenv:Envelope>