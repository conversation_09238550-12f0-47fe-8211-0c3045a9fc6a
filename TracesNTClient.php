<?php



class TracesNTClient {
    private const ACCEPTANCE_ENDPOINT = 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ReferenceDataServiceV1';
    private const PRODUCTION_ENDPOINT = 'https://webgate.ec.europa.eu/tracesnt/ws/ReferenceDataServiceV1';
    
    private $username;
    private $password;
    private $clientId;
    private $useProduction;
    
    public function __construct($username, $password, $clientId, $useProduction = false) {
        $this->username = $username;
        $this->password = $password;
        $this->clientId = $clientId;
        $this->useProduction = $useProduction;
    }
    
    /**
     * Generate WS-Security headers for SOAP request
     */
    private function createSecurityHeaders() {
        $nonce = $this->generateNonce();
        $created = $this->getCurrentTimestamp();
        $passwordDigest = $this->generatePasswordDigest($nonce, $created, $this->password);
        
        $wsseNS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
        $wsuNS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd';
        
        // Calculate expires time (2 minutes from now)
        $expires = date('Y-m-d\TH:i:s.v\Z', strtotime('+2 minutes'));
        
        $securityHeader = '
        <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="'.$wsseNS.'">
            <wsse:UsernameToken wsu:Id="UsernameToken-'.time().'" xmlns:wsu="'.$wsuNS.'">
                <wsse:Username>'.$this->username.'</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">'.$passwordDigest.'</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">'.$nonce.'</wsse:Nonce>
                <wsu:Created>'.$created.'</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="Timestamp-'.time().'">
                <wsu:Created>'.$created.'</wsu:Created>
                <wsu:Expires>'.$expires.'</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode xmlns:v3="http://traces-nt.ec.europa.eu/ws/v3">en</v3:LanguageCode>
        <v3:WebServiceClientId xmlns:v3="http://traces-nt.ec.europa.eu/ws/v3">'.$this->clientId.'</v3:WebServiceClientId>';
        
        return $securityHeader;
    }
    
    /**
     * Get list of available classification trees
     */
    public function getClassificationTrees() {
        $endpoint = $this->useProduction ? self::PRODUCTION_ENDPOINT : self::ACCEPTANCE_ENDPOINT;
        
        $soapRequest = $this->createSoapRequest(
            $endpoint,
            '<getClassificationTrees xmlns="http://referencedata.ws.tracesnt.ec.europa.eu/"/>'
        );
        
        $response = $this->sendSoapRequest($endpoint, $soapRequest);
        return $this->parseClassificationTreesResponse($response);
    }
    
    /**
     * Get specific classification tree details
     */
    public function getClassificationTree($treeId) {
        $endpoint = $this->useProduction ? self::PRODUCTION_ENDPOINT : self::ACCEPTANCE_ENDPOINT;
        
        $soapRequest = $this->createSoapRequest(
            $endpoint,
            '<getClassificationTree xmlns="http://referencedata.ws.tracesnt.ec.europa.eu/">
                <treeId>'.$treeId.'</treeId>
            </getClassificationTree>'
        );
        
        $response = $this->sendSoapRequest($endpoint, $soapRequest);
        return $this->parseClassificationTreeResponse($response);
    }
    
    /**
     * Get certificate model information
     */
    public function getCertificateModel($modelId) {
        $endpoint = $this->useProduction ? self::PRODUCTION_ENDPOINT : self::ACCEPTANCE_ENDPOINT;
        
        $soapRequest = $this->createSoapRequest(
            $endpoint,
            '<getCertificateModel xmlns="http://referencedata.ws.tracesnt.ec.europa.eu/">
                <modelId>'.$modelId.'</modelId>
            </getCertificateModel>'
        );
        
        $response = $this->sendSoapRequest($endpoint, $soapRequest);
        return $this->parseCertificateModelResponse($response);
    }
    
    /**
     * Create SOAP request with security headers
     */
    private function createSoapRequest($endpoint, $body) {
        $securityHeaders = $this->createSecurityHeaders();
        
        $soapRequest = '<?xml version="1.0" encoding="UTF-8"?>
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
                          xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
                          xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                          xmlns:v3="http://traces-nt.ec.europa.eu/ws/v3">
            <soapenv:Header>
                '.$securityHeaders.'
            </soapenv:Header>
            <soapenv:Body>
                '.$body.'
            </soapenv:Body>
        </soapenv:Envelope>';
        
        return $soapRequest;
    }
    
    /**
     * Send SOAP request to endpoint
     */
    private function sendSoapRequest($endpoint, $soapRequest) {
        $headers = array(
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: ""',
            'Content-Length: ' . strlen($soapRequest)
        );
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $soapRequest);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        
        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($httpCode != 200) {
            throw new Exception('HTTP error: ' . $httpCode . ' - ' . $response);
        }
        
        curl_close($ch);
        
        return $response;
    }
    
    // Utility methods for WS-Security
    private function generateNonce() {
        $nonce = openssl_random_pseudo_bytes(16);
        return base64_encode($nonce);
    }
    
    private function getCurrentTimestamp() {
        return gmdate('Y-m-d\TH:i:s.v\Z');
    }
    
    private function generatePasswordDigest($nonce, $created, $password) {
        $nonceBytes = base64_decode($nonce);
        $createdBytes = $created;
        $passwordBytes = $password;
        
        // Concatenate nonce + created + password
        $combined = $nonceBytes . $createdBytes . $passwordBytes;
        
        // Generate SHA-1 digest
        $digest = sha1($combined, true);
        
        return base64_encode($digest);
    }
    
    // Response parsing methods
    private function parseClassificationTreesResponse($response) {
        $xml = simplexml_load_string($response);
        if ($xml === false) {
            throw new Exception('Failed to parse XML response');
        }
        
        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xml->registerXPathNamespace('ns', 'http://referencedata.ws.tracesnt.ec.europa.eu/');
        
        $trees = array();
        $results = $xml->xpath('//ns:getClassificationTreesResponse/ns:return');
        
        foreach ($results as $result) {
            $trees[] = (string)$result;
        }
        
        return $trees;
    }
    
    private function parseClassificationTreeResponse($response) {
        return $this->parseXmlResponse($response, 'getClassificationTreeResponse');
    }
    
    private function parseCertificateModelResponse($response) {
        return $this->parseXmlResponse($response, 'getCertificateModelResponse');
    }
    
    private function parseXmlResponse($response, $responseElement) {
        $xml = simplexml_load_string($response);
        if ($xml === false) {
            throw new Exception('Failed to parse XML response');
        }
        
        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xml->registerXPathNamespace('ns', 'http://referencedata.ws.tracesnt.ec.europa.eu/');
        
        $result = $xml->xpath('//ns:'.$responseElement.'/ns:return');
        if (isset($result[0])) {
            return $result[0]->asXML();
        }
        
        return $response;
    }
    
    /**
     * Print SOAP message for debugging
     */
    public function printSoapMessage($message) {
        $dom = new DOMDocument();
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($message);
        echo $dom->saveXML();
    }
}

// Example usage
try {
    // Replace with your actual credentials3
    $client = new TracesNTClient(
        'your-username',
        'your-password',
        'your-client-id',
        false // Use acceptance environment for testing
    );
    
    // Test getting classification trees
    echo "Fetching classification trees...\n";
    $trees = $client->getClassificationTrees();
    echo "Found " . count($trees) . " classification trees\n";
    
    // Test getting a specific tree (replace with actual ID)
    if (!empty($trees)) {
        $treeDetails = $client->getClassificationTree('CHED-PP');
        echo "Tree details: " . substr($treeDetails, 0, 100) . "...\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>