DG Sante
Unit G4 “Official Controls”

UN/CEFACT WebService for CHED retrieve V2
TRACES NT

Date:
Doc. Version:
Doc. Reference:

07/02/2025
1.0
TNT-WS-CHED-RET-V2

This template is based on PM² V2.5
For the latest version of this template please visit the PM² Wiki

Commission européenne, B-1049 Bruxelles / Europese Commissie, B-1049 Brussel - Belgium. Telephone: (32-2) 299 11 11.
Office: 05/45. Telephone: direct line (32-2) 2999659.
Commission européenne, L-2920 Luxembourg. Telephone: (352) 43 01-1.

TRACES NT UN/CEFACT WebService for CHED retrieve V2

Document Control Information
Settings
Document Title:
Project Title:
Document Author:
Project Owner:
Project Manager:
Doc. Version:
Sensitivity:
Date:

Value
UN/CEFACT WebService for CHED retrieve V2
TRACES NT
Traces Team
DG Sante G.4
SKARINGER Lars
1.0
Basic
07/02/2025

Document Approver(s) and Reviewer(s):
NOTE: All Approvers are required. Records of each approver must be maintained. All Reviewers in the list are
considered required unless explicitly listed as Optional.
Name

Role

Action

Date

Document history:
The Document Author is authorized to make the following types of changes to the document without requiring
that the document be re-approved:
• Editorial, formatting, and spelling
• Clarification
To request a change to this document, contact the Document Author or Owner.
Changes to this document are summarized in the following table in reverse chronological order (latest version
first).
Revision Date
Created by
Short Description of Changes
1.0
07/02/2025 AMELIO Francesco
Initiated document from TNT-WS-CHED-RET-V01 version 1.7

Configuration Management: Document Location
The latest version of this document is stored in CIRCA BC.

TABLE OF CONTENTS

Page 2 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

1 INTRODUCTION ...................................................................................................................................4
1.1 Purpose and scope of this document ................................................................................................4
Definitions used in this document .............................................................................................................4
1.2 Standards and versions......................................................................................................................4
2 AUTHENTICATION ...............................................................................................................................5
2.1 SOAP Headers ....................................................................................................................................5
3 SERVICE DESCRIPTION .........................................................................................................................6
3.1 Endpoints ...........................................................................................................................................6
3.2 WebService methods.........................................................................................................................6
3.2.1 Get CHED ....................................................................................................................................6
3.2.2 Get CHED PDF .............................................................................................................................6
3.2.3 Get CHED signed PDF ..................................................................................................................6
3.2.4 Get CHED signed XML .................................................................................................................6
3.2.5 Find CHED ...................................................................................................................................7
3.2.6 Get CHED status ..........................................................................................................................7
3.2.7 Get CHED Follow-Up (part III) .....................................................................................................7
3.2.8 Get CHED laboratory tests ..........................................................................................................7
3.2.9 Get CHED details on non-compliance .........................................................................................7
4 RESPONSE ...........................................................................................................................................7
4.1 Operators, authorities and controlled locations identifiers ..............................................................8
4.1.1 Override default returned identifier ...........................................................................................9
5 ACCOMPANYING DOCUMENTS DOWNLOAD SERVICE .........................................................................9
5.1 Endpoints .........................................................................................................................................10
6 ERRORS .............................................................................................................................................10
7 APPENDIX: DATA DICTIONARY ..........................................................................................................11
7.1 List of parameters for the find CHED method .................................................................................11
7.2 Details returned by the find CHED and get CHED status methods ..................................................15
7.2.1 Definition of the type SPSClassificationType ............................................................................19
7.2.2 Definition of the type SPSReferencedDocumentType ..............................................................19
8 APPENDIX: REFERENCES AND RELATED DOCUMENTS ........................................................................20

Page 3 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

1

INTRODUCTION

1.1

Purpose and scope of this document

This document is intended to provide some details for CHED retrieval service to connect via Web Services to the
TRACES NT system as well as information on Acceptance and Production environments.
Note:

Please refer to the "TNT–WebServices-authentication" on how to configure the WebService
authentication.

Definitions used in this document
• TRACES NT is the EU information system used to manage veterinary certificates to and from the EU.
• UN/CEFACT is the United Nations Centre for Trade Facilitation and Electronic Business.
• CHED is Common Health Entry Document. More general information about the CHED can be found on the
TRACES-NT help page: https://webgate.ec.europa.eu/cfcas3/tracesnt-webhelp/Default.htm
• CHED Part I refers to the consignment part of the document. Typically submitted by a responsible for load
or in specific cases by a competent authority.
• CHED Part II refers to the decision part of the document. It’s submitted by a competent authority.

1.2

Standards and versions

The CHED interface uses the
https://www.unece.org/cefact.html.

UN/CEFACT

standards

described

on

the

UNECE

website

In particular, the CHED document is mapped to Sanitary/Phytosanitary (SPS) Certificate element.
Note:

Please refer to the excel document “TNT-UN-CEFACT-Mappings-CHED-V2” for more details on the
mapping between the CHED document and the SPSCertificate.

The XML Schema of the UN/CEFACT standard could be downloaded on the UNECE website
https://www.unece.org/cefact/xml_schemas/index. The version used for this release is the 17A.
Anyhow the UN/CEFACT XML Schemas needed for the CHED interface are encapsulated into the WSDL available
on the TNT environments, so there is no need to download the UN/CEFACT XML Schemas from the UNECE
website.

Page 4 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

2

AUTHENTICATION

As indicated in the document "TNT–WebServices-Authentication” the SOAP message should include WSSecurity elements and additional other elements in the header part in order for the client to correctly
authenticate with TRACES NT Web Services.
This is a sample header:
<soapenv:Envelope>
<soapenv:Header>
<wsse:Security
xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecuritysecext-1.0.xsd"
xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurityutility-1.0.xsd">
<wsse:UsernameToken wsu:Id="A3380A376973ABE6A6150840400087524">
<wsse:Username>username</wsse:Username>
<wsse:Password
Type="wsse:PasswordDigest">nuQbmp5F6bWH5AlQRLRRxWonQ=</wsse:Password>
<wsse:Nonce>aaa3aSJqORu6x62BdNNczw==</wsse:Nonce>
<wsu:Created>2017-10-19T08:44:51Z</wsu:Created>
</wsse:UsernameToken>
<wsu:Timestamp wsu:Id="TS-A3380A376973ABE6A6150840400087423">
<wsu:Created>2017-10-19T09:10:40.874Z</wsu:Created>
<wsu:Expires>2017-10-19T09:12:40.874Z</wsu:Expires>
</wsu:Timestamp>
</wsse:Security>
<v3:LanguageCode>en</v3:LanguageCode>
<v3:WebServiceClientId>clientId</v3:WebServiceClientId>
</soap:Header>
<soap:Body>
...
</soap:Body>
</soap:Envelope>

2.1

SOAP Headers

There is no extra SOAP header needed to retrieve or find a CHED document. Anyhow the optional LanguageCode
header can be used if the client wants to have the translatable text or PDF in another language other than
English.

Page 5 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

3

SERVICE DESCRIPTION

This Web Service will be used by operators, competent authorities or responsible for load to retrieve a CHED
document. The client can retrieve the CHED document in XML format or in PDF format.
The system provides also a functionality to search CHED documents by using a set of criteria, or to know the
status of a list of CHED documents.

3.1

Endpoints

Below the endpoints for the CHED Web service available in TRACES NT:
Environment

Endpoint

Acceptance

https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2?wsdl

Production

https://webgate.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2?wsdl

3.2

WebService methods

The following web methods are available to the Responsible for load and to the Authorities

3.2.1 Get CHED
Method name: getChedCertificate
Description: Retrieves Part I (consignment) and Part II of a CHED document.
Input: A CHED reference number.
Output: The CHED document in the SPSCertificate format.
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException

3.2.2 Get CHED PDF
Method name: getChedPdfCertificate
Description: Retrieves the PDF of Part I (consignment) and Part II of a CHED document.
Input: CHED Reference number and a List of ExtraLanguageCode to generate the PDF in more than one language.
The first language code will be always the one set in the SOAP Header.
Output: The PDF file in form of binary object (in Base64 format).
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException,
IllegalLanguageCodeException

3.2.3 Get CHED signed PDF
Method name: getChedSignedPdfCertificate
Description: Retrieves the PDF of Part I (consignment) and Part II of a CHED document.
Input: CHED Reference number
Output: The PDF file in form of binary object (in Base64 format).
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException,
SignedChedNotAvailableException

3.2.4 Get CHED signed XML
Method name: getChedSignedXmlCertificate
Description: Retrieves the digitally signed XML (if available)
Input: CHED Reference number

Page 6 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

Output: The XML file in form of binary object (in Base64 format).
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException,
SignedChedNotAvailableException

3.2.5 Find CHED
Method name: findChedCertificate
Description: Search for CHED documents using a set of criteria
Input: a set of criteria (See appendix “List of parameters for the find” for more info).
Output: A subset of the basic CHED information (See appendix: “Details returned by the find CHED and get CHED
status” for more info).
Faults: IllegalFindChedCertificateException

3.2.6 Get CHED status
Method name: getChedCertificateStatus
Description: Retrieves basic information on a list of CHED documents
Input: List of CHED Reference numbers
Output: A subset of the basic CHED information (See appendix: ”Details returned by the find CHED and get CHED
status” for more info).
Faults: ChedCertificatesNotFoundException

3.2.7 Get CHED Follow-Up (part III)
Method name: getChedFollowUp
Description: Retrieves Part III (follow-up) of a CHED document.
Input: A CHED reference number.
Output: A list of ChedFollowUp elements.
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException

3.2.8 Get CHED laboratory tests
Method name: getChedLaboratoryTests
Description: Retrieves the list of laboratory tests for a given CHED.
Input: A CHED reference number.
Output: A list of SPSConsignmentItemLaboratoryTest elements.
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException

3.2.9 Get CHED details on non-compliance
Method name: getChedNonComplianceDetails
Description: Retrieves the details on non-compliance for a specific CHED-PP
Input: A CHED reference number.
Output: A ChedNonComplianceDetailsType element as described in the document TNT-WS-CHED-DNP-MAP-V01
Faults: ChedCertificateNotFoundException, ChedCertificatePermissionDeniedException

4

RESPONSE

If the request is successfully processed, the system will reply with a SPSCertificate element. The XML will be
returned with elements according to the mapping described in the document “UN-

Page 7 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

CEFACT_Mappings_TNT.CHED”. Each CHED type (i.e. CHED-PP, CHED-D etc) will have its own excel Sheet as the
mapping might slightly differ based on the type.

4.1

Operators, authorities and controlled locations identifiers

All operators (i.e. a Responsible for load, Exporter, Importer), Authorities (i.e. the Border control post) and
Controlled locations (i.e. control points, onward transportation facilities etc) are mapped into two types of
elements: SPSPartyType and SPSLocationType as in the following examples.
The type of ID is specified in the schemeID attribute.
<ConsigneeSPSParty>
<ID schemeID="eori_number">FR12345678</ID>
<Name/>
<SpecifiedSPSAddress>
<CountryID>FR</CountryID>
</SpecifiedSPSAddress>
</ConsigneeSPSParty>

Code 1 Reference to a Consignee using the EORI Number “FR12345678”
<ActivityAuthorizedSPSParty>
<ID schemeID="controlled_destination_id">892245</ID>
<Name/>
<SpecifiedSPSAddress>
<CountryID>DK</CountryID>
</SpecifiedSPSAddress>
</ActivityAuthorizedSPSParty>

Code 2 Reference to a Controlled location using the controlled location id “892245”
<UnloadingBaseportSPSLocation>
<ID schemeID="un_locode">BEBRU</ID>
<Name>BE</Name>
</UnloadingBaseportSPSLocation>

Code 3 Example of a Border control post using the UN Locode ”BEBRU”
The system will normally returns the first available ID type (specified in the schemeID attribute), depending on
the type of entity, following the order specified in the table below:
Table 1 Order of returned identifiers by type
ID.schemeID
Operators

operator_activity_id
eori_number
national_registry_number
vat
operator_internal_activity_id

Border control posts

un_locode
authority_activity_id

Authority activities

authority_activity_id
un_locode

Controlled locations

controlled_destination_id
un_locode

Laboratory

laboratory_code
laboratory_internal_identifier
laboratory_activity_reference_number

Please note that when an operator is a non-business operator (or unregistered), the ID element is missing, as in
the following example:
<ConsignorSPSParty>
<Name>Green Exports Ltd.</Name>

Page 8 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

<SpecifiedSPSAddress>
<PostcodeCode>21107</PostcodeCode>
<LineOne>Kiwi alley 45</LineOne>
<CityName languageID="en">Buenos Aires</CityName>
<CountryID>AR</CountryID>
<CountrySubDivisionName languageID="en">Region S1</CountrySubDivisionName>
</SpecifiedSPSAddress>
</ConsignorSPSParty>

Code 4 - Example of non-business (or unregistered) consignor

4.1.1 Override default returned identifier
It is possible to override the priority of the returned identifier and force the system to return a specific type of
identifier by setting a list of attributes in the SOAP Header.
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
xmlns:v4="http://ec.europa.eu/sanco/tracesnt/base/v4" xmlns:oas="http://docs.oasisopen.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
xmlns:v01="http://ec.europa.eu/tracesnt/certificate/ched/v01">
<soapenv:Header>
<v3:LanguageCode>en</v3:LanguageCode>
<v3:WebServiceClientId>ws-client-id</v3:WebServiceClientId>
<v3:Attributes>
<v3:Attribute key="operator_activity_preferred_scheme_id">eori_number</v3:Attribute>
<v3:Attribute key="bcp_preferred_scheme_id">authority_activity_id</v3:Attribute>
<v3:Attribute key="authority_activity_preferred_scheme_id">un_locode</v3:Attribute>
<v3:Attribute key="controlled_location_preferred_scheme_id">un_locode</v3:Attribute>
</v3:Attributes>
<oas:Security/>
</soapenv:Header>
<soapenv:Body>
<v01:GetChedCertificateRequest>
<v01:ID>CHEDPP.IT.2020.1000091</v01:ID>
</v01:GetChedCertificateRequest>
</soapenv:Body>
</soapenv:Envelope>

Code 5 Example of attributes to override the default returned identifiers
The following tables described the attribute key and the possible values
Attribute.key

Attribute possible value

Operators

operator_activity_preferred_scheme_id

operator_activity_id
eori_number
national_registry_number
vat
operator_internal_activity_id

Border
control
posts

bcp_preferred_scheme_id

un_locode
authority_activity_id

Authority
activities

authority_activity_preferred_scheme_id

authority_activity_id
un_locode

Controlled
locations

controlled_location_preferred_scheme_id

controlled_destination_id
un_locode

5

ACCOMPANYING DOCUMENTS DOWNLOAD SERVICE

The accompanying documents attached to a CHED are mapped to the element SPSCertificate /
SPSExchangedDocument / ReferenceSPSReferencedDocument. This element will never contain a binary content
but only meta-data on the attachment.
This accompanying document download web service can be used to retrieve binary attachments.
In order to download the attachment, the client will need to specify the following parameters:
Page 9 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

-

CHED Reference number
Filename
Document ID

Given a CHED with reference number CHEDPP.IT.2019.0000001, containing an accompanying document with
filename "FV-000262.pdf" and the documentId is “123456”:
<ReferenceSPSReferencedDocument>
<IssueDateTime>2016-12-01T00:00:00.000+01:00</IssueDateTime>
<TypeCode name="Dangerous goods declaration">890</TypeCode>
<RelationshipTypeCode name="Mutually defined reference number (Supporting
document)">ZZZ</RelationshipTypeCode>
<ID schemeAgencyID="PK">LT-123898</ID>
<AttachmentBinaryObject uri="uri:documentid:123456" filename="FV-000262.pdf"/>
<Information>Brussels</Information>
</ReferenceSPSReferencedDocument>

The request to retrieve the attachment will look like this:
<GetCertificateAttachmentRequest>
<ChedCertificateReference>CHEDPP.IT.2019.0000001</ChedCertificateReference>
<FileName>FV-000262.pdf</FileName>
<DocumentId>123456</DocumentId>
<GetCertificateAttachmentRequest>

The endpoint is using SOAP 1.2, and supports the MTOM with XOP attachments. In order to enable the binary
transmission optimization the client will need to add an additional HTTP header “Accept: application/xop+xml;”

5.1

Endpoints

Below the endpoints for the Certificate attachments web service available in TRACES NT:

6

Environment

Endpoint

Acceptance

https://webgate.acceptance.ec.europa.eu/tracesnt/ws/CertificateAttachmentsServiceV1?wsdl

Production

https://webgate.ec.europa.eu/tracesnt/ws/CertificateAttachmentsServiceV1?wsdl

ERRORS

In case of permission denied or CHED not found errors, the system will always return a SOAP Fault.
•
•
•
•
•

ChedCertificateNotFoundException
Fault returned in case the certificate specified in the get method is not found.
ChedCertificatePermissionDeniedException
Fault returned in case the user doesn’t have the sufficient permissions to retrieve the data.
IllegalLanguageCodeException
In case the language code specified in the get PDF is not valid
IllegalFindChedCertificateException
This fault is returned when the find parameters are invalid.
ChedCertificatesNotFoundException
Fault returned in case the get request to get the status for a list of certificates contains a non-existing
CHED reference number.

Page 10 of 20

TRACES NT UN/CEFACT WebService for CHED retrieve V2

7

APPENDIX: DATA DICTIONARY

7.1

List of parameters for the find CHED method

Name

Content

Cardinality

Note

pageSize

Integer

1..1

pageSize > 1 and <= 200
Number of results returned in a page.
The client should stop requesting new pages when the
results retrieved in the page are less than the pageSize
specified.

offset

Integer

1..1

offset > 0
Number of results to be skipped. To implement pagination,
and retrieve the next page, set an offset as the pageSize
multiplied by the number of page to be retrieved.

Type

A (Animals)

0..10

P (Products)
PP (Plants)
D (Article D).
N (NOA – Notification of arrival certificate)
Status

Date: 07/02/2025

47

Draft

1

New

42

Decision signed as in progress

97

Authorized for onward transportation

146

Authorized for transhipment

11 / 20

0..20

Version: 1.0

Type of CHED

TRACES NT UN/CEFACT WebService for CHED retrieve V2

Purpose

124

Authorized or transfer to

35

Authorized or onward travel

99

Authorized for transit

41

Rejected

122

Partially rejected

70

Validated

55
64

Deleted
Cancelled

44

Replaced

68

Split

FREE_CIRCULATION

Free circulation

PRIVATE_IMPORT

For private
import

TRANSHIPMENT_TO_ANOTHER_COUNTRY

For
transhipment to
another country
For transit to
another country

TRANSIT_TO_ANOTHER_COUNTRY

Date: 07/02/2025

RE_ENTRY

For re-entry

TEMPORARY_ADMISSION

For temporary
admission

12 / 20

Version: 1.0

0..1

TRACES NT UN/CEFACT WebService for CHED retrieve V2

NON_CONFORMING_GOODS

TRANSFER
ONWARD_TRAVEL
TRANSIT_TO_US_OR_NATO_BASE

For nonconforming
goods
For transfer
For onward
travel
For transit to US
or NATO base

Transhipped

Boolean (true)

0..1

If set, the system will retrieve only CHEDs documents that are
originating from a CHED that has status “Authorized for
transshipment”.

Transfer

Boolean (true)

0..1

If set, the system will retrieve only CHEDs documents that are
originating from a CHED that has status “Authorized for
transfer”.

HasPlantHealthNonCompliance

Boolean (true)

0..1

If set, the system will retrieve only CHEDs documents that
have plant health non compliance

HasPendingLaboratoryTests

Boolean (true)

0..1

If set, the system will retrieve only CHEDs documents that
have pending laboratory tests

RequiresFollowUp

Boolean (true)

0..1

If set, the system will retrieve only CHEDs documents that
require a follow-up

IsDigitallySigned

Boolean (true)

0..1

When indicated, will return only CHEDs that are digitally
signed, either in XML or PDF format.

LocalReference

String (80)

0..1

Field I.3

Date: 07/02/2025

13 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

PhytoReference

String (30)

0..1

Field 2.a of Phyto certificate

PhytoLocalReference

String (80)

0..1

Field 2.b of Phyto certificate

CNCode

String (regular expression: [0-9][0-9]){1,10})

0..10

CN Code (See I.31).

CNCode.exactMatch

Boolean (true,false)

0..1

If set to true, the search will return only CHEDs with exact
match of the commodity’s and packaging material’s CN Code.
If the attribute is not specified or set to false, the search is
performed wildcard-wise (starts with).

BCPCodeOrUnLocode

String

0..1

Border control post activity code or UN/Locode of the Box I.4.
The search is performed with an approximate string matching
(fuzzy) so it might find codes that are not 100% matching the
parameter specified.

CountryOfIssuance

Country code (ISO 3166-1 alpha-2)

0..1

Country where the certificate have been validated (issued). Is
the country of the authority that signed the Part II of the CHED
(see field II.21).
The field is searchable only when the CHED decision is signed.

CountryOfEntry

Country code (ISO 3166-1 alpha-2)

0..1

The country of the Border Control Post (box I.4)

CountryOfDispatch

Country code (ISO 3166-1 alpha-2)

0..1

The country of Export/Dispatch (box I.14)

CountryOfOrigin

Country code (ISO 3166-1 alpha-2)

0..1

See box I.11

CountryOfPlaceOfDestination

Country code (ISO 3166-1 alpha-2)

0..1

See box I.7

CountryOfConsignor

Country code (ISO 3166-1 alpha-2)

0..1

See box I.1

CountryOfConsignee

Country code (ISO 3166-1 alpha-2)

0..1

See box I.6

Date: 07/02/2025

14 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

CountryOfTransferControlPoint

Country code (ISO 3166-1 alpha-2)

0..1

Country of the Authority or Control point where additional
controls will take place (See I.20).

CreateDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of physical creation in the DB

UpdateDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the last update to the CHED. The update can
concern Part I, Part II, any status change, any laboratory test
and control.

StatusChangeDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the last change of status (i.e. the CHED
changed from “Decision signed as in progress” to “Validated”)

DeclarationDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the declaration of the Part I (consignment)

DecisionDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the signature of the Part II (decision)

PriorNotificationDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of prior notification (I.10)

7.2

Details returned by the find CHED and get CHED status methods

Name

Content

Cardinality

Note

pageSize

Integer

1..1

Number of results returned in a current page.
The client should stop requesting new pages when the
results retrieved in the page are less than the pageSize
specified.

offset

Integer

1..1

Number of results skipped.

Type

A (Animals)

1..1

Type of CHED

P (Products)
PP (Plants)
D (Article D)

Date: 07/02/2025

15 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

N (NOA – Notification of arrival certificate)
ID

String(30)

1..1

CHED ID (i.e. CHEDPP.FR.2017.0500612)

LocalReference

String (80)

0..1

Field I.3

Status

47

Draft

1..1

1

New

42

Decision signed as in progress

97

Authorized for onward transportation

146

Authorized for transhipment

124

Authorized or transfer to

35

Authorized or onward travel

99

Authorized for transit

41

Rejected

122

Partially rejected

70

Validated

55
64

Deleted
Cancelled

44

Replaced

40

Recalled

68

Split

CommodityApplicableSPSClassification

List of SPSClassificationType (see paragraph 7.2.1)

0..n

List of commodity classifications.
Only SPSClassificationType with SystemID = ‘CN’ will be
returned.
For the format see paragraph 7.2.1 and the document:
UN-CEFACT_Mappings_TNT.CHED

PackagingMaterialApplicableSPSClassification

List of SPSClassificationType (see paragraph 7.2.1)

0..n

List of packaging material classifications.
Only SPSClassificationType with SystemID = ‘CN’ will be
returned.

Date: 07/02/2025

16 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

For the format see paragraph 7.2.1 and the document:
UN-CEFACT_Mappings_TNT.CHED
CreateDateTime

Date time

1..1

UpdateDateTime

Date time

1..1

StatusChangeDateTime

Date time

0..1

DeclarationDateTime

Date time

0..1

DecisionDateTime

Date time

0..1

BCPCode

String (40)

0..1

See box I.4. Authority code of the Border Control Post (i.e.
ESMAD4)

BCPUnLocode

String (5)

0..1

See box I.4. UN/LOCODE of the Border Control Post (i.e.
ESMAD)

CountryOfIssuance

Country code (ISO 3166-1 alpha-2)

0..1

Country where the certificate have been validated (issued),
normally is the country of the BCP, except in some cases.
In case of CHEDs originating from a document that is
"Authorized for movement to approved place of
destination", the issuing country is the country of the
Approved place of destination.
The field is present only when the certificate has passed a
Validated status.

CountryOfEntry

Country code (ISO 3166-1 alpha-2)

0..1

Country of the Border Control Post (box I.4)

CountryOfDispatch

Country code (ISO 3166-1 alpha-2)

0..1

The country of Export/Dispatch (box I.14)

CountryOfOrigin

List of Country code (ISO 3166-1 alpha-2)

0..n

List of countries of origin (box I.11)

CountryOfPlaceOfDestination

Country code (ISO 3166-1 alpha-2)

0..1

Country of the place of destination (box I.7)

CountryOfTransferControlPoint

Country code (ISO 3166-1 alpha-2)

0..1

Country of the transfer control authority or of the control
point (I.20)

Date: 07/02/2025

17 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

CountryOfConsignor

Country code (ISO 3166-1 alpha-2)

0..1

Country of the consignor (box I.1)

CountryOfConsignee

Country code (ISO 3166-1 alpha-2)

0..1

Country of the consignee (box I.6)

ConsignorName

String (200)

0..1

See box I.1

ConsigneeName

String (200)

0..1

See box I.6

ReferenceSPSReferencedDocument

List of SPSReferencedDocumentType (see paragraph 0)

0..n

List of references to other documents and certificates,
including the Phyto certificates (cloned from), the
references to replaced CHEDs and to other related CHEDs
and the list of accompanying documents. For the format
see paragraph 0 and the document: UNCEFACT_Mappings_TNT.CHED

Date: 07/02/2025

18 / 20

Version: 1.0

TRACES NT UN/CEFACT WebService for CHED retrieve V2

7.2.1 Definition of the type SPSClassificationType
The SPSClassification type is defined by a SystemID as key and a ClassCode as value plus some extra
descriptive elements such as the SystemName and a list of ClassName.
Example of SPSClassificationType describing a CN Code
<ApplicableSPSClassification>
<SystemID>CN</SystemID>
<SystemName>CN Code (Combined Nomenclature)</SystemName>
<ClassCode>0908</ClassCode>
<ClassName languageID="en">09 COFFEE, TEA, MATÉ AND SPICES</ClassName>
<ClassName languageID="en">0908 Hazelnuts or filberts (Corylus spp.)</ClassName>
</ApplicableSPSClassification>

7.2.2 Definition of the type SPSReferencedDocumentType
This type is composed mainly of a TypeCode, a RelationshipTypeCode, and an ID. Other optional elements,
used only is specific contexts, are the IssueDateTime, AttachmentBinaryObject and Information.
Only the types listed below are returned:
TypeCode

RelationshipTypeCode

Description

916
960
610
763

AVZ
ACE
ACE
ACE
ACW
AIR

Local reference
Customs document reference
Transhipment by
Transhipment of
Replaces
Replaced by
Split from
Split in
Clones (PHYTO certificate reference 2.a)
Clones (PHYTO certificate local reference 2.b)
Clones (EU-Import field I.2.a.)
Clones (EU-Import field I.2.)
Clones (COI – Certificate of Organic Import – Box 3)
Controlled for "transfer to" in (CHED reference number)
Controlled for "transfer to" of (CHED reference number)
Partially rejected/validated in
Partially rejected/validated from

636
76
636
851
852
856
841
840
76

AXG
ACE
AVZ
ACE
AVZ
ACE
ACE
ACE
ACE
AIR

The interface returns also the list of Supporting documents (with RelationshipTypeCode = ZZZ). For more
information see the mapping document.
Example of SPSReferencedDocumenType describing a Customs document reference:
<ReferenceSPSReferencedDocument>
<TypeCode name="Single administrative document (Customs document
reference)">960</TypeCode>
<RelationshipTypeCode name="Related document number">ACE</RelationshipTypeCode>
<ID>6711435</ID>
</ReferenceSPSReferencedDocument>

Date: 07/02/2025
1.0

19 / 20

Version:

TRACES NT UN/CEFACT WebService for CHED retrieve V2

APPENDIX: REFERENCES AND RELATED DOCUMENTS

8
ID

Reference or Related Document

Source or
Link/Location

TNT-WS-AUTH

TNT-WebServices-Authentication

CIRCA-BC

TNT-WS-SOAPUI

TNT-SoapUI-WS-Security-Configuration

CIRCA-BC

TNT-WS-CHED-MAP-V2

TNT-UN-CEFACT-Mappings-CHED-V2.xlsx

CIRCA-BC

TNT-WS-CHED-FU-MAP-V01

TNT-UN-CEFACT-Mappings-CHED-Follow-UpV01.xlsx

CIRCA-BC

TNT-WS-CHED-DNC-MAPV01

TNT-UN-CEFACT-Mappings-CHED-NonCompliance-V01

CIRCA-BC

TNT-WS-MD-LAB-MAP-V1

TNT-WebServices-Metatada-MappingLaboratoryTests-V1.xlsx

CIRCA-BC
(Metadata
documentation
package)

Date: 07/02/2025
1.0

20 / 20

Version:

